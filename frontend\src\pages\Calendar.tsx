import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../context/AuthContext';
import { getCalendarEvents, createEvent, completeEvent } from '../services/eventService';
import { getAllUsers } from '../services/userService';
import { getAllCustomersNoPage } from '../services/customerService';
import { Event } from '../types/event';
import { User } from '../types/user';
import { Customer } from '../types/customer';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import LoadingSpinner from '../components/LoadingSpinner';
import EventModal from '../components/EventModal';
import { FaCalendarAlt, FaFilter, FaPlus } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useConfirmation } from '../context/ConfirmationContext';
import { useLocation, useNavigate } from 'react-router-dom';

const Calendar: React.FC = () => {
  const { user } = useAuth();
  const { showConfirmation } = useConfirmation();
  const location = useLocation();
  const navigate = useNavigate();
  const calendarRef = useRef<FullCalendar>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [newEvent, setNewEvent] = useState({
    customer_id: null as number | null,
    event_type: '',
    description: '',
    scheduled_date: '',
    user_ids: [] as number[]
  });
  const [selectedUserId, setSelectedUserId] = useState<number | null>(
    user?.role !== 'administrator' ? user?.id : null
  );
  const [selectedCustomerId, setSelectedCustomerId] = useState<number | null>(null);
  const [submitting, setSubmitting] = useState(false);

  // Get customerId from URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const customerIdParam = urlParams.get('customerId');
    if (customerIdParam) {
      setSelectedCustomerId(parseInt(customerIdParam));
    }
  }, [location.search]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch events for calendar
        const eventsResponse = await getCalendarEvents(
          undefined,
          undefined,
          selectedUserId || undefined,
          selectedCustomerId || undefined
        );
        setEvents(eventsResponse.events);

        // Fetch users for filtering (admin only)
        if (user?.role === 'administrator') {
          const usersResponse = await getAllUsers();
          setUsers(usersResponse.users);
        }

        // Fetch customers for event creation and filtering
        const customersResponse = await getAllCustomersNoPage();
        setCustomers(customersResponse.customers);

      } catch (err) {
        console.error('Error fetching calendar data:', err);
        setError('Failed to load calendar data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user, selectedUserId, selectedCustomerId]);

  const handleDateClick = (info: any) => {
    if (user?.role === 'administrator') {
      const clickedDate = new Date(info.dateStr);
      // Set time to noon
      clickedDate.setHours(12, 0, 0);

      setNewEvent({
        customer_id: null,
        event_type: '',
        description: '',
        scheduled_date: clickedDate.toISOString().slice(0, 16),
        user_ids: []
      });

      setShowModal(true);
    }
  };

  const handleEventClick = (info: any) => {
    const eventId = parseInt(info.event.id);
    const clickedEvent = events.find(e => e.id === eventId);

    if (clickedEvent) {
      setSelectedEvent(clickedEvent);

      if (clickedEvent.status === 'pending' && user?.role === 'administrator') {
        showConfirmation({
          title: 'Event Actions',
          message: `What would you like to do with this event: "${clickedEvent.description}"?`,
          confirmText: 'Complete Event',
          cancelText: 'Edit Event',
          confirmButtonClass: 'bg-green-600 hover:bg-green-700',
          cancelButtonClass: 'bg-blue-600 hover:bg-blue-700',
          onConfirm: () => handleCompleteEvent(clickedEvent.id),
          onCancel: () => {
            setNewEvent({
              customer_id: clickedEvent.customer_id,
              event_type: clickedEvent.event_type,
              description: clickedEvent.description,
              scheduled_date: clickedEvent.scheduled_date.slice(0, 16),
              user_ids: clickedEvent.user_ids || []
            });
            setShowModal(true);
          }
        });
      } else if (clickedEvent.status === 'pending' && user?.role !== 'administrator') {
        showConfirmation({
          title: 'Complete Event',
          message: `Would you like to mark this event as completed: "${clickedEvent.description}"?`,
          confirmText: 'Complete',
          confirmButtonClass: 'bg-green-600 hover:bg-green-700',
          onConfirm: () => handleCompleteEvent(clickedEvent.id)
        });
      }
    }
  };

  const handleCompleteEvent = async (eventId: number) => {
    try {
      await completeEvent(eventId);

      // Update the local events state
      setEvents(prevEvents =>
        prevEvents.map(event =>
          event.id === eventId
            ? { ...event, status: 'completed', completed_at: new Date().toISOString() }
            : event
        )
      );

      toast.success('Event marked as completed');
    } catch (err) {
      console.error('Error completing event:', err);
      toast.error('Failed to complete event');
    }
  };

  const handleCreateEvent = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const formattedDate = new Date(newEvent.scheduled_date).toISOString();
      const createdEvent = await createEvent(
        newEvent.customer_id,
        newEvent.event_type,
        newEvent.description,
        formattedDate,
        newEvent.user_ids.length > 0 ? newEvent.user_ids : null
      );

      setEvents([...events, createdEvent]);
      setShowModal(false);
      toast.success('Event created successfully');
    } catch (err) {
      console.error('Error creating event:', err);
      setError('Failed to create event');
      toast.error('Failed to create event');
    } finally {
      setSubmitting(false);
    }
  };

  const handleUserFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setSelectedUserId(value === '' ? null : parseInt(value));
  };

  const handleCustomerFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    const customerId = value === '' ? null : parseInt(value);
    setSelectedCustomerId(customerId);

    // Update URL parameters
    const urlParams = new URLSearchParams(location.search);
    if (customerId) {
      urlParams.set('customerId', customerId.toString());
    } else {
      urlParams.delete('customerId');
    }

    const newSearch = urlParams.toString();
    const newUrl = newSearch ? `${location.pathname}?${newSearch}` : location.pathname;
    navigate(newUrl, { replace: true });
  };

  const getEventColor = (event: Event) => {
    if (event.status === 'completed') {
      return '#10b981'; // Green for completed events
    }

    // Check if document has expiry status
    if (event.document?.expiry_status) {
      switch (event.document.expiry_status) {
        case 'red':
          return '#ef4444'; // Red for urgent
        case 'orange':
          return '#f97316'; // Orange for warning
        default:
          return '#3b82f6'; // Blue for normal
      }
    }

    return '#3b82f6'; // Default blue
  };

  if (loading) {
    return <LoadingSpinner message="Loading calendar..." />;
  }

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-8 space-y-3 sm:space-y-0">
        <div className="flex items-center">
          <FaCalendarAlt className="text-amspm-primary mr-2 sm:mr-3 hidden sm:block" size={30} />
          <FaCalendarAlt className="text-amspm-primary mr-2 sm:hidden" size={24} />
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-amspm-text">Events Calendar</h1>
            {selectedCustomerId && (
              <p className="text-sm text-gray-600 mt-1">
                Showing events for: {customers.find(c => c.id === selectedCustomerId)?.name}
              </p>
            )}
          </div>
        </div>

        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
          {user?.role === 'administrator' && (
            <div className="flex items-center space-x-2">
              <FaFilter className="text-amspm-text" />
              <select
                value={selectedUserId?.toString() || ''}
                onChange={handleUserFilterChange}
                className="input py-1 px-2 text-sm"
              >
                <option value="">All Users</option>
                {users.map(u => (
                  <option key={u.id} value={u.id.toString()}>
                    {u.name || u.email}
                  </option>
                ))}
              </select>
            </div>
          )}

          <div className="flex items-center space-x-2">
            <FaFilter className="text-amspm-text" />
            <select
              value={selectedCustomerId?.toString() || ''}
              onChange={handleCustomerFilterChange}
              className="input py-1 px-2 text-sm"
            >
              <option value="">All Customers</option>
              {customers.map(c => (
                <option key={c.id} value={c.id.toString()}>
                  {c.name}
                </option>
              ))}
            </select>
          </div>

          {user?.role === 'administrator' && (
            <button
              onClick={() => {
                setNewEvent({
                  customer_id: selectedCustomerId,
                  event_type: '',
                  description: '',
                  scheduled_date: new Date().toISOString().slice(0, 16),
                  user_ids: []
                });
                setShowModal(true);
              }}
              className="btn btn-secondary flex items-center text-sm sm:text-base w-full sm:w-auto"
            >
              <FaPlus className="mr-2" /> Create Event
            </button>
          )}
        </div>
      </div>

      {error && <p className="text-red-500 mb-4">{error}</p>}

      <div className="card bg-white shadow-sm p-0 overflow-hidden">
        <div className="p-4">
          <FullCalendar
            ref={calendarRef}
            plugins={[dayGridPlugin, interactionPlugin]}
            initialView="dayGridMonth"
            headerToolbar={{
              left: 'prev,next today',
              center: 'title',
              right: 'dayGridMonth,dayGridWeek'
            }}
            events={events.map(event => ({
              id: event.id.toString(),
              title: `${event.event_type}${event.customer_name ? ': ' + event.customer_name : ''}`,
              start: event.scheduled_date,
              end: event.scheduled_date,
              backgroundColor: getEventColor(event),
              borderColor: getEventColor(event),
              classNames: [event.status === 'completed' ? 'completed-event' : 'pending-event'],
              extendedProps: {
                description: event.description,
                status: event.status,
                customer: event.customer_name,
                type: event.event_type
              }
            }))}
            eventTimeFormat={{
              hour: '2-digit',
              minute: '2-digit',
              meridiem: false
            }}
            eventContent={(eventInfo) => {
              return (
                <div className="fc-event-main-frame" style={{ padding: '1px' }}>
                  <div className="fc-event-title-container">
                    <div className="fc-event-title">
                      {eventInfo.event.extendedProps.type}
                    </div>
                    {eventInfo.event.extendedProps.customer && (
                      <div className="text-xs overflow-hidden text-ellipsis whitespace-nowrap">
                        {eventInfo.event.extendedProps.customer}
                      </div>
                    )}
                  </div>
                </div>
              );
            }}
            dateClick={handleDateClick}
            eventClick={handleEventClick}
            height="auto"
            dayMaxEvents={4}
            eventMaxStack={3}
            moreLinkClick="popover"
            fixedWeekCount={false}
            aspectRatio={1.8}
          />
        </div>
      </div>

      <div className="mt-4 flex flex-wrap gap-2">
        <div className="flex items-center">
          <span className="inline-block w-4 h-4 rounded-full bg-blue-500 mr-2"></span>
          <span className="text-sm">Normal Event</span>
        </div>
        <div className="flex items-center">
          <span className="inline-block w-4 h-4 rounded-full bg-orange-500 mr-2"></span>
          <span className="text-sm">Warning (Expiring Soon)</span>
        </div>
        <div className="flex items-center">
          <span className="inline-block w-4 h-4 rounded-full bg-red-500 mr-2"></span>
          <span className="text-sm">Urgent (Expired)</span>
        </div>
        <div className="flex items-center">
          <span className="inline-block w-4 h-4 rounded-full bg-green-500 mr-2"></span>
          <span className="text-sm">Completed</span>
        </div>
      </div>

      {showModal && (
        <EventModal
          event={newEvent}
          onClose={() => setShowModal(false)}
          onSubmit={handleCreateEvent}
          setEvent={setNewEvent}
          isEditing={false}
          submitting={submitting}
          customers={customers}
          users={users}
        />
      )}
    </div>
  );
};

export default Calendar;
