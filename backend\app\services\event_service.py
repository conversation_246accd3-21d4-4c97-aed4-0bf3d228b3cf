from app.repositories.event_repository import EventRepository
from app.repositories.user_repository import UserRepository
from app.repositories.customer_repository import CustomerRepository
from app.repositories.document_repository import DocumentRepository

from typing import List, Dict, Optional
from datetime import datetime, timedelta, timezone
from sqlalchemy import func
from app import db
from app.models.event import Event
from app.utils.cache_utils import clear_cache_for_entity
import logging

logger = logging.getLogger(__name__)

VALID_EVENT_TYPES = [
    "offerte",
    "werkbon",
    "onderhoudsbon",
    "onderhoudscontract",
    "meldkamercontract",
    "beveiligingscertificaat",
    "intakedocument",
    "projectietekening",
    "beveiligingsplan",
    "kabeltekeningen",
    "checklist oplevering installatie"
]

class EventService:
    def __init__(self, event_repo, customer_repo, user_repo, document_repo=None):
        self.event_repo = event_repo
        self.customer_repo = customer_repo
        self.user_repo = user_repo
        self.document_repo = document_repo or DocumentRepository()


    def get_all_events(self, page: int = 1, per_page: int = 20) -> Dict:
        events, total = self.event_repo.get_all(page, per_page)
        return {
            "events": [event.to_dict() for event in events],
            "total": total,
            "page": page,
            "per_page": per_page
        }

    def get_events_by_user(self, user_id: int, page: int = 1, per_page: int = 20) -> Dict:
        events, total = self.event_repo.get_by_user_id(user_id, page, per_page)
        return {
            "events": [event.to_dict() for event in events],
            "total": total,
            "page": page,
            "per_page": per_page
        }

    def get_event_by_id(self, event_id: int) -> Dict:
        event = self.event_repo.get_by_id(event_id)
        if not event:
            raise Exception("Event not found")
        return event.to_dict()

    def create_event(self, event_type: str, description: str, scheduled_date: str, customer_id: int = None, user_ids: List[int] = None, document_id: int = None) -> Dict:
        if event_type not in VALID_EVENT_TYPES:
            raise ValueError(f"Invalid event type. Must be one of: {', '.join(VALID_EVENT_TYPES)}")

        # Convert 0 to None for optional IDs
        customer_id = None if customer_id == 0 else customer_id
        document_id = None if document_id == 0 else document_id

        # Handle user_ids - filter out 0 values and convert to None if empty
        if user_ids:
            user_ids = [uid for uid in user_ids if uid != 0]
            if not user_ids:
                user_ids = None

        if customer_id:
            customer = self.customer_repo.get_by_id(customer_id)
            if not customer:
                raise Exception("Customer not found")

        if user_ids:
            # Check if all assigned users exist
            for user_id in user_ids:
                user = self.user_repo.get_by_id(user_id)
                if not user:
                    raise Exception(f"Assigned user with ID {user_id} not found")

        scheduled_date = datetime.fromisoformat(scheduled_date)
        event = self.event_repo.create(customer_id, event_type, description, scheduled_date, user_ids, document_id)

        # Clear cache for events
        clear_cache_for_entity('event')
        if user_ids:
            clear_cache_for_entity('user_events')
        clear_cache_for_entity('calendar_events')
        clear_cache_for_entity('dashboard_metrics')

        return event.to_dict()

    # Legacy method for backward compatibility
    def create_event_legacy(self, event_type: str, description: str, scheduled_date: str, customer_id: int = None, user_id: int = None, document_id: int = None) -> Dict:
        user_ids = [user_id] if user_id else None
        return self.create_event(event_type, description, scheduled_date, customer_id, user_ids, document_id)

    def update_event(self, event_id: int, event_type: str, description: str, scheduled_date: str, customer_id: int = None, user_ids: List[int] = None, document_id: int = None) -> Dict:
        if event_type not in VALID_EVENT_TYPES:
            raise ValueError(f"Invalid event type. Must be one of: {', '.join(VALID_EVENT_TYPES)}")

        event = self.event_repo.get_by_id(event_id)
        if not event:
            raise Exception("Event not found")

        # Store original user_ids for cache invalidation
        original_user_ids = [user.id for user in event.users]

        # Convert 0 to None for optional IDs
        customer_id = None if customer_id == 0 else customer_id
        document_id = None if document_id == 0 else document_id

        # Handle user_ids - filter out 0 values and convert to None if empty
        if user_ids:
            user_ids = [uid for uid in user_ids if uid != 0]
            if not user_ids:
                user_ids = None

        if customer_id:
            customer = self.customer_repo.get_by_id(customer_id)
            if not customer:
                raise Exception("Customer not found")

        if user_ids:
            # Check if all assigned users exist
            for user_id in user_ids:
                user = self.user_repo.get_by_id(user_id)
                if not user:
                    raise Exception(f"Assigned user with ID {user_id} not found")

        scheduled_date = datetime.fromisoformat(scheduled_date)
        event = self.event_repo.update(event, customer_id, event_type, description, scheduled_date, user_ids, document_id)

        # Clear cache for events
        clear_cache_for_entity('event', event_id)
        clear_cache_for_entity('event')

        # Clear user events cache if users changed
        if original_user_ids or user_ids:
            clear_cache_for_entity('user_events')

        clear_cache_for_entity('calendar_events')
        clear_cache_for_entity('dashboard_metrics')

        return event.to_dict()

    # Legacy method for backward compatibility
    def update_event_legacy(self, event_id: int, event_type: str, description: str, scheduled_date: str, customer_id: int = None, user_id: int = None, document_id: int = None) -> Dict:
        user_ids = [user_id] if user_id else None
        return self.update_event(event_id, event_type, description, scheduled_date, customer_id, user_ids, document_id)

    def complete_event(self, event_id: int, completed_by_user_id: int = None) -> Dict:
        event = self.event_repo.get_by_id(event_id)
        if not event:
            raise Exception("Event not found")

        # Store user_ids for cache invalidation
        user_ids = [user.id for user in event.users]

        event = self.event_repo.complete(event, completed_by_user_id)

        # Clear cache for events
        clear_cache_for_entity('event', event_id)
        clear_cache_for_entity('event')
        if user_ids:
            clear_cache_for_entity('user_events')
        clear_cache_for_entity('calendar_events')
        clear_cache_for_entity('dashboard_metrics')

        return event.to_dict()

    def delete_event(self, event_id: int) -> bool:
        # Get the event first to access its properties before deletion
        event = self.event_repo.get_by_id(event_id)
        if not event:
            raise Exception("Event not found")

        # Store user_ids for cache invalidation
        user_ids = [user.id for user in event.users]

        success = self.event_repo.delete(event_id)
        if not success:
            raise Exception("Failed to delete event")

        # Clear cache for events
        clear_cache_for_entity('event', event_id)
        clear_cache_for_entity('event')
        if user_ids:
            clear_cache_for_entity('user_events')
        clear_cache_for_entity('calendar_events')
        clear_cache_for_entity('dashboard_metrics')

        return True

    def get_dashboard_metrics(self) -> Dict:
        """Get metrics for the admin dashboard"""
        # Get event counts by status
        pending_count = db.session.query(func.count(Event.id)).filter(Event.status == "pending").scalar() or 0
        completed_count = db.session.query(func.count(Event.id)).filter(Event.status == "completed").scalar() or 0
        total_events = pending_count + completed_count

        # Get events by type
        event_types_query = db.session.query(Event.event_type, func.count(Event.id)).group_by(Event.event_type).all()
        event_types = {event_type: count for event_type, count in event_types_query}

        # Get upcoming events (next 7 days)
        now = datetime.now(timezone.utc)
        next_week = now + timedelta(days=7)
        upcoming_events_count = db.session.query(func.count(Event.id)).filter(
            Event.status == "pending",
            Event.scheduled_date >= now,
            Event.scheduled_date <= next_week
        ).scalar() or 0

        # Get user counts by role
        from app.models.user import User
        user_roles_query = db.session.query(
            func.count(User.id)
        ).scalar() or 0

        # Get customer count
        from app.models.customer import Customer
        customer_count = db.session.query(
            func.count(Customer.id)
        ).scalar() or 0

        # Get document expiration metrics
        from app.models.document import Document

        # Documents expiring soon (next 60 days)
        expiring_soon = db.session.query(func.count(Document.id)).filter(
            Document.expiry_date.isnot(None),
            Document.expiry_date >= now,
            Document.expiry_date <= now + timedelta(days=60),
            Document.status == "active"
        ).scalar() or 0

        # Documents already expired
        expired = db.session.query(func.count(Document.id)).filter(
            Document.expiry_date.isnot(None),
            Document.expiry_date < now,
            Document.status == "active"
        ).scalar() or 0

        # Documents by expiry status
        red_status = db.session.query(func.count(Document.id)).filter(
            Document.expiry_date.isnot(None),
            Document.expiry_date < now,
            Document.status == "active"
        ).scalar() or 0

        orange_status = db.session.query(func.count(Document.id)).filter(
            Document.expiry_date.isnot(None),
            Document.expiry_date >= now,
            Document.expiry_date <= now + timedelta(days=60),
            Document.status == "active"
        ).scalar() or 0

        green_status = db.session.query(func.count(Document.id)).filter(
            Document.expiry_date.isnot(None),
            Document.expiry_date > now + timedelta(days=60),
            Document.status == "active"
        ).scalar() or 0

        # Total active documents
        total_active_documents = db.session.query(func.count(Document.id)).filter(
            Document.status == "active"
        ).scalar() or 0

        return {
            "events": {
                "total": total_events,
                "pending": pending_count,
                "completed": completed_count,
                "upcoming": upcoming_events_count,
                "by_type": event_types
            },
            "users": {
                "total": user_roles_query
            },
            "customers": {
                "total": customer_count
            },
            "documents": {
                "total_active": total_active_documents,
                "expiring_soon": expiring_soon,
                "expired": expired,
                "by_status": {
                    "red": red_status,
                    "orange": orange_status,
                    "green": green_status
                }
            }
        }

    def get_calendar_events(self, start_date: str = None, end_date: str = None, user_id: int = None, customer_id: int = None) -> List[Dict]:
        """Get events for calendar view with optional date range, user, and customer filtering"""
        from app.models.user import User
        query = db.session.query(Event)

        # Apply date filters if provided
        if start_date:
            start = datetime.fromisoformat(start_date)
            query = query.filter(Event.scheduled_date >= start)

        if end_date:
            end = datetime.fromisoformat(end_date)
            query = query.filter(Event.scheduled_date <= end)

        # Filter by user if provided (using many-to-many relationship)
        if user_id:
            query = query.join(Event.users).filter(User.id == user_id)

        # Filter by customer if provided
        if customer_id:
            query = query.filter(Event.customer_id == customer_id)

        events = query.all()
        return [event.to_dict() for event in events]

    def get_events_by_quotation(self, quotation_id: int) -> List[Dict]:
        """
        Get events related to a quotation.
        This is used to check if an installation event already exists for a quotation.

        Args:
            quotation_id: The ID of the quotation

        Returns:
            List[Dict]: A list of events related to the quotation
        """
        # Get the quotation to find the customer_id
        from app.repositories.quotation_repository import QuotationRepository
        quotation_repo = QuotationRepository()
        quotation = quotation_repo.get_by_id(quotation_id)

        if not quotation:
            raise Exception("Quotation not found")

        # Get all installation events for this customer
        installation_events = self.event_repo.get_events_by_customer_and_type(
            quotation.customer_id,
            "checklist oplevering installatie"
        )

        return [event.to_dict() for event in installation_events]

    def get_events_by_document(self, document_id: int, event_type: str = None) -> List[Dict]:
        """
        Get events related to a document.
        This is used to check if an installation event already exists for a document.

        Args:
            document_id: The ID of the document
            event_type: Optional event type to filter by

        Returns:
            List[Dict]: A list of events related to the document
        """
        # Get events by document_id
        events = Event.query.filter(Event.document_id == document_id)

        # Filter by event_type if provided
        if event_type:
            events = events.filter(Event.event_type == event_type)

        return [event.to_dict() for event in events.all()]
